# Health Check Router for database connectivity and AWS Secrets Manager authentication

from fastapi import APIRouter, HTTPException, status
from fastapi.responses import JSONResponse
from db.db import get_connection_manager
from db.secrets_manager_auth import get_secrets_auth
import logging
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/health/database", tags=["Health Checks"])
async def check_database_health():
    # Test the current database connection
    try:
        from db.connection_manager import initialize_connection_manager
        
        # Initialize connection manager (this will trigger credential retrieval and logging)
        connection_manager = initialize_connection_manager(
            secret_name="stg_member_db_rw_user",
            region="us-east-1"
        )
        test_result = connection_manager.test_connection()
        
        if test_result["status"] == "success":
            logger.info("Database health check passed")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "status": "healthy",
                    "timestamp": datetime.now().isoformat(),
                    "database_info": test_result,
                    "connection_info": connection_manager.get_connection_info(),
                    "logs": connection_manager.get_logs(),
                    "message": "Database connection is healthy"
                }
            )
        else:
            logger.error(f"Database health check failed: {test_result}")
            return JSONResponse(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                content={
                    "status": "unhealthy",
                    "timestamp": datetime.now().isoformat(),
                    "database_info": test_result,
                    "connection_info": connection_manager.get_connection_info(),
                    "logs": connection_manager.get_logs(),
                    "message": "Database connection failed",
                    "troubleshooting_steps": [
                        "Check Lambda execution role has 'secretsmanager:GetSecretValue' permission",
                        "Verify secret 'stg_member_db_rw_user' exists in us-east-1 region",
                        "Check database server is accessible",
                        "Verify database credentials are correct"
                    ]
                }
            )
            
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error(f"Database health check error: {str(e)}")
        logger.error(f"Traceback: {error_traceback}")

        # Get detailed error information from secrets manager
        detailed_error = None
        try:
            from db.db import get_connection_manager
            connection_manager = get_connection_manager()
            if hasattr(connection_manager, 'secrets_auth'):
                detailed_error = connection_manager.secrets_auth.get_detailed_error()
        except Exception as detail_error:
            logger.error(f"Failed to get detailed error: {str(detail_error)}")

        error_content = {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": error_traceback,
            "message": "Health check failed due to internal error",
            "troubleshooting_steps": [
                "Check Lambda execution role has 'secretsmanager:GetSecretValue' permission",
                "Verify secret 'stg_member_db_rw_user' exists in us-east-1 region",
                "Check CloudWatch logs for detailed error messages",
                "Verify AWS credentials are properly configured"
            ]
        }

        # Add detailed error information if available
        if detailed_error:
            error_content["detailed_authentication_error"] = detailed_error

        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=error_content
        )

@router.get("/health/database/connection-info", tags=["Health Checks"])
async def get_database_connection_info():
    # Get information about the current database connection configuration
    try:
        connection_manager = get_connection_manager()
        connection_info = connection_manager.get_connection_info()
        
        logger.info("Database connection info retrieved")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "status": "success",
                "timestamp": datetime.now().isoformat(),
                "connection_info": connection_info,
                "message": "Database connection information retrieved successfully"
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get database connection info: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "message": "Failed to retrieve database connection information"
            }
        )

@router.post("/health/database/refresh-connection", tags=["Health Checks"])
async def refresh_database_connection():
    # Force refresh the database connection
    try:
        connection_manager = get_connection_manager()
        refresh_result = connection_manager.refresh_connection()

        if refresh_result["status"] == "success":
            logger.info("Database connection refresh successful")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "status": "success",
                    "timestamp": datetime.now().isoformat(),
                    "refresh_result": refresh_result,
                    "message": "Database connection refreshed successfully"
                }
            )
        else:
            logger.error(f"Database connection refresh failed: {refresh_result}")
            return JSONResponse(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                content={
                    "status": "failed",
                    "timestamp": datetime.now().isoformat(),
                    "refresh_result": refresh_result,
                    "message": "Database connection refresh failed"
                }
            )

    except Exception as e:
        logger.error(f"Database connection refresh error: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "message": "Connection refresh failed due to internal error"
            }
        )

@router.get("/health/database/secrets-info", tags=["Health Checks"])
async def get_secrets_info():
    # Get information about the current AWS Secrets Manager configuration
    try:
        # Initialize connection manager first to ensure secrets auth is available
        get_connection_manager()
        secrets_auth = get_secrets_auth()
        secrets_info = secrets_auth.get_secret_info()

        # Get detailed error information even for successful responses
        detailed_error = secrets_auth.get_detailed_error()

        logger.info("Secrets info retrieved")
        response_content = {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "secrets_info": secrets_info,
            "message": "Secrets Manager information retrieved successfully"
        }

        # Add detailed error information if available
        if detailed_error and detailed_error.get("step") != "no_error":
            response_content["detailed_error"] = detailed_error

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=response_content
        )

    except Exception as e:
        logger.error(f"Failed to get secrets info: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "message": "Failed to retrieve Secrets Manager information"
            }
        )

@router.get("/health/database/test-secret-access", tags=["Health Checks"])
async def test_secret_access():
    # Test access to AWS Secrets Manager without retrieving sensitive data
    try:
        from db.secrets_manager_auth import initialize_secrets_auth
        
        # Initialize secrets auth directly
        secrets_auth = initialize_secrets_auth("stg_member_db_rw_user", "us-east-1")
        test_result = secrets_auth.test_secret_access()

        # Get detailed error information
        detailed_error = secrets_auth.get_detailed_error()

        if test_result["status"] == "success":
            logger.info("Secret access test passed")
            response_content = {
                "status": "success",
                "timestamp": datetime.now().isoformat(),
                "test_result": test_result,
                "logs": test_result.get("logs", {}),
                "message": "Secret access test successful"
            }

            # Add detailed error information if available
            if detailed_error and detailed_error.get("step") != "no_error":
                response_content["detailed_error"] = detailed_error

            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=response_content
            )
        else:
            logger.error(f"Secret access test failed: {test_result}")
            return JSONResponse(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                content={
                    "status": "failed",
                    "timestamp": datetime.now().isoformat(),
                    "test_result": test_result,
                    "logs": test_result.get("logs", {}),
                    "message": "Secret access test failed",
                    "troubleshooting_steps": [
                        "Check Lambda execution role has 'secretsmanager:GetSecretValue' permission",
                        "Verify secret 'stg_member_db_rw_user' exists in us-east-1 region",
                        "Ensure Lambda is in the correct VPC if required",
                        "Check CloudWatch logs for detailed error messages"
                    ]
                }
            )

    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error(f"Secret access test error: {str(e)}")
        logger.error(f"Traceback: {error_traceback}")
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "error_type": type(e).__name__,
                "traceback": error_traceback,
                "message": "Secret access test failed due to internal error",
                "troubleshooting_steps": [
                    "Check Lambda execution role has 'secretsmanager:GetSecretValue' permission",
                    "Verify secret 'stg_member_db_rw_user' exists in us-east-1 region",
                    "Check CloudWatch logs for detailed error messages",
                    "Verify AWS credentials are properly configured"
                ]
            }
        )

@router.get("/health/system", tags=["Health Checks"])
async def check_system_health():
    # Comprehensive system health check including database and secrets manager status
    try:
        # Get connection manager (this will initialize if needed)
        connection_manager = get_connection_manager()

        # Test database connection
        db_test = connection_manager.test_connection()

        # Get secrets auth (should be available after connection manager init)
        secrets_auth = get_secrets_auth()

        # Test secret access
        secret_test = secrets_auth.test_secret_access()

        # Get connection info
        connection_info = connection_manager.get_connection_info()

        # Determine overall health status
        db_healthy = db_test["status"] == "success"
        secrets_healthy = secret_test["status"] == "success"
        overall_status = "healthy" if db_healthy and secrets_healthy else "unhealthy"
        status_code = status.HTTP_200_OK if overall_status == "healthy" else status.HTTP_503_SERVICE_UNAVAILABLE

        logger.info(f"System health check completed: {overall_status}")

        return JSONResponse(
            status_code=status_code,
            content={
                "status": overall_status,
                "timestamp": datetime.now().isoformat(),
                "components": {
                    "database": {
                        "status": db_test["status"],
                        "details": db_test
                    },
                    "secrets_manager": {
                        "status": secret_test["status"],
                        "details": secret_test
                    },
                    "connection_manager": {
                        "status": "healthy",
                        "details": connection_info
                    }
                },
                "message": f"System health check completed - {overall_status}"
            }
        )
        
    except Exception as e:
        logger.error(f"System health check error: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "message": "System health check failed due to internal error"
            }
        )

@router.get("/health/debug/client-creation-test", tags=["Health Checks"])
async def test_client_creation_debug():
    """Debug endpoint to test only boto3 client creation using IAM role"""
    debug_info: dict = {
        "timestamp": datetime.now().isoformat(),
        "test": "boto3_client_creation_iam_role"
    }

    try:
        # Test boto3 client creation using IAM role (default credentials)
        import boto3
        client = boto3.client(
            'secretsmanager',
            region_name='us-east-1'
        )

        debug_info["result"] = {
            "status": "success",
            "message": "boto3 client created successfully using IAM role",
            "client_type": str(type(client)),
            "authentication_method": "IAM role"
        }

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=debug_info
        )

    except Exception as e:
        debug_info["result"] = {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__,
            "message": "boto3 client creation failed"
        }
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=debug_info
        )

@router.get("/health/debug/secret-retrieval-test", tags=["Health Checks"])
async def test_secret_retrieval_debug():
    """Debug endpoint to test client creation + secret retrieval using IAM role"""
    debug_info: dict = {
        "timestamp": datetime.now().isoformat(),
        "test": "client_creation_and_secret_retrieval_iam"
    }

    try:
        # Step 1: Create boto3 client using IAM role
        import boto3
        client = boto3.client(
            'secretsmanager',
            region_name='us-east-1'
        )

        debug_info["step1_client_creation"] = {
            "status": "success",
            "message": "boto3 client created successfully using IAM role",
            "authentication_method": "IAM role"
        }

        # Step 2: Get secret value
        response = client.get_secret_value(SecretId='stg_member_db_rw_user')
        secret_string = response['SecretString']

        debug_info["step2_secret_retrieval"] = {
            "status": "success",
            "message": "Secret retrieved successfully",
            "secret_length": len(secret_string),
            "response_keys": list(response.keys())
        }

        # Step 3: Parse secret JSON
        import json
        credentials = json.loads(secret_string)

        debug_info["step3_secret_parsing"] = {
            "status": "success",
            "message": "Secret parsed successfully",
            "credential_keys": list(credentials.keys()),
            "has_username": bool(credentials.get('username')),
            "has_password": bool(credentials.get('password')),
            "has_host": bool(credentials.get('host')),
            "has_port": bool(credentials.get('port')),
            "has_dbname": bool(credentials.get('dbname'))
        }

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=debug_info
        )

    except Exception as e:
        debug_info["error"] = {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__,
            "message": f"Failed at some step in the process"
        }
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=debug_info
        )
