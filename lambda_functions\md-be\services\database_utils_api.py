import logging
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
from fastapi import HTTPException
from utils.response_utils import success_response, internal_server_error_response

logger = logging.getLogger(__name__)


def reset_all_sequences(db: Session) -> Dict[str, Any]:
    """
    Reset all PostgreSQL sequences for tables with auto-incrementing primary keys.
    
    This function:
    1. Discovers all sequences in the database that are associated with table columns
    2. For each sequence, finds the maximum ID value in the corresponding table
    3. Resets the sequence to start from max_id + 1
    
    Args:
        db: SQLAlchemy database session
        
    Returns:
        Dictionary containing the results of the sequence reset operation
        
    Raises:
        HTTPException: If there's an error during the sequence reset process
    """
    try:
        logger.info("Starting sequence reset operation")
        
        # Query to find all sequences and their associated tables/columns
        sequence_query = text("""
            SELECT 
                s.schemaname,
                s.sequencename,
                t.schemaname as table_schema,
                t.tablename,
                a.attname as column_name,
                pg_get_serial_sequence(quote_ident(t.schemaname)||'.'||quote_ident(t.tablename), a.attname) as full_sequence_name
            FROM 
                pg_sequences s
            JOIN 
                pg_depend d ON d.objid = (
                    SELECT oid 
                    FROM pg_class 
                    WHERE relname = s.sequencename 
                    AND relnamespace = (
                        SELECT oid 
                        FROM pg_namespace 
                        WHERE nspname = s.schemaname
                    )
                )
            JOIN 
                pg_class t ON t.oid = d.refobjid
            JOIN 
                pg_attribute a ON a.attrelid = t.oid AND a.attnum = d.refobjsubid
            JOIN 
                pg_namespace tn ON tn.oid = t.relnamespace
            WHERE 
                d.deptype = 'a'  -- auto dependency
                AND t.relkind = 'r'  -- regular table
                AND tn.nspname = 'public'  -- only public schema
                AND s.schemaname = 'public'
            ORDER BY 
                t.relname, a.attname;
        """)
        
        # Execute the query to get all sequences
        result = db.execute(sequence_query)
        sequences = result.fetchall()
        
        if not sequences:
            logger.warning("No sequences found in the database")
            return success_response(
                "No sequences found to reset",
                {"sequences_reset": [], "total_sequences": 0}
            )
        
        reset_results = []
        successful_resets = 0
        failed_resets = 0
        
        for seq in sequences:
            try:
                schema_name = seq.schemaname
                sequence_name = seq.sequencename
                table_schema = seq.table_schema
                table_name = seq.tablename
                column_name = seq.column_name
                full_sequence_name = seq.full_sequence_name
                
                logger.info(f"Processing sequence: {full_sequence_name} for table {table_name}.{column_name}")
                
                # Get the current maximum ID from the table
                max_id_query = text(f"""
                    SELECT COALESCE(MAX({column_name}), 0) as max_id 
                    FROM {table_schema}.{table_name}
                """)
                
                max_id_result = db.execute(max_id_query)
                max_id = max_id_result.scalar()
                
                # Get current sequence value before reset
                current_val_query = text(f"SELECT last_value FROM {full_sequence_name}")
                current_val_result = db.execute(current_val_query)
                current_value = current_val_result.scalar()
                
                # Reset the sequence to max_id + 1 (or 1 if table is empty)
                if max_id is None:
                    max_id = 0
                new_value = max(max_id + 1, 1)
                
                reset_query = text(f"SELECT setval('{full_sequence_name}', {new_value}, false)")
                db.execute(reset_query)
                
                # Verify the reset worked
                verify_query = text(f"SELECT last_value FROM {full_sequence_name}")
                verify_result = db.execute(verify_query)
                new_sequence_value = verify_result.scalar()
                
                reset_info = {
                    "sequence_name": full_sequence_name,
                    "table_name": f"{table_schema}.{table_name}",
                    "column_name": column_name,
                    "max_table_id": max_id,
                    "previous_sequence_value": current_value,
                    "new_sequence_value": new_sequence_value,
                    "status": "success"
                }
                
                reset_results.append(reset_info)
                successful_resets += 1
                
                logger.info(f"Successfully reset sequence {full_sequence_name}: {current_value} -> {new_sequence_value}")
                
            except Exception as seq_error:
                error_info = {
                    "sequence_name": getattr(seq, 'full_sequence_name', f"{seq.schemaname}.{seq.sequencename}"),
                    "table_name": f"{seq.table_schema}.{seq.tablename}",
                    "column_name": seq.column_name,
                    "status": "failed",
                    "error": str(seq_error)
                }
                
                reset_results.append(error_info)
                failed_resets += 1
                
                logger.error(f"Failed to reset sequence {seq.sequencename}: {str(seq_error)}")
        
        # Commit all changes
        db.commit()
        
        logger.info(f"Sequence reset operation completed. Success: {successful_resets}, Failed: {failed_resets}")
        
        return success_response(
            f"Sequence reset completed. {successful_resets} sequences reset successfully, {failed_resets} failed.",
            {
                "sequences_reset": reset_results,
                "total_sequences": len(sequences),
                "successful_resets": successful_resets,
                "failed_resets": failed_resets,
                "summary": {
                    "operation": "sequence_reset",
                    "timestamp": str(db.execute(text("SELECT NOW()")).scalar()),
                    "success_rate": f"{(successful_resets / len(sequences) * 100):.1f}%" if sequences else "0%"
                }
            }
        )
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error during sequence reset operation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "message": f"Failed to reset sequences: {str(e)}",
                "status_code": 500
            }
        )


def get_sequence_status(db: Session) -> Dict[str, Any]:
    """
    Get the current status of all sequences and their corresponding table max IDs.

    This is a read-only operation that shows which sequences might be out of sync.

    Args:
        db: SQLAlchemy database session

    Returns:
        Dictionary containing sequence status information
    """
    try:
        logger.info("Getting sequence status information")

        # Same query as reset function but for status checking
        sequence_query = text("""
            SELECT
                s.schemaname,
                s.sequencename,
                t.schemaname as table_schema,
                t.tablename,
                a.attname as column_name,
                pg_get_serial_sequence(quote_ident(t.schemaname)||'.'||quote_ident(t.tablename), a.attname) as full_sequence_name
            FROM
                pg_sequences s
            JOIN
                pg_depend d ON d.objid = (
                    SELECT oid
                    FROM pg_class
                    WHERE relname = s.sequencename
                    AND relnamespace = (
                        SELECT oid
                        FROM pg_namespace
                        WHERE nspname = s.schemaname
                    )
                )
            JOIN
                pg_class t ON t.oid = d.refobjid
            JOIN
                pg_attribute a ON a.attrelid = t.oid AND a.attnum = d.refobjsubid
            JOIN
                pg_namespace tn ON tn.oid = t.relnamespace
            WHERE
                d.deptype = 'a'  -- auto dependency
                AND t.relkind = 'r'  -- regular table
                AND tn.nspname = 'public'  -- only public schema
                AND s.schemaname = 'public'
            ORDER BY
                t.relname, a.attname;
        """)

        result = db.execute(sequence_query)
        sequences = result.fetchall()

        if not sequences:
            return success_response(
                "No sequences found in the database",
                {"sequences": [], "total_sequences": 0}
            )

        sequence_status = []
        out_of_sync_count = 0

        for seq in sequences:
            try:
                table_schema = seq.table_schema
                table_name = seq.tablename
                column_name = seq.column_name
                full_sequence_name = seq.full_sequence_name

                # Get the current maximum ID from the table
                max_id_query = text(f"""
                    SELECT COALESCE(MAX({column_name}), 0) as max_id
                    FROM {table_schema}.{table_name}
                """)
                max_id_result = db.execute(max_id_query)
                max_id = max_id_result.scalar() or 0

                # Get current sequence value
                current_val_query = text(f"SELECT last_value FROM {full_sequence_name}")
                current_val_result = db.execute(current_val_query)
                current_value = current_val_result.scalar() or 0

                # Check if sequence is out of sync
                is_out_of_sync = current_value <= max_id
                if is_out_of_sync:
                    out_of_sync_count += 1

                status_info = {
                    "sequence_name": full_sequence_name,
                    "table_name": f"{table_schema}.{table_name}",
                    "column_name": column_name,
                    "max_table_id": max_id,
                    "current_sequence_value": current_value,
                    "is_out_of_sync": is_out_of_sync,
                    "recommended_next_value": max_id + 1 if max_id >= 0 else 1
                }

                sequence_status.append(status_info)

            except Exception as seq_error:
                error_info = {
                    "sequence_name": getattr(seq, 'full_sequence_name', f"{seq.schemaname}.{seq.sequencename}"),
                    "table_name": f"{seq.table_schema}.{seq.tablename}",
                    "column_name": seq.column_name,
                    "status": "error",
                    "error": str(seq_error)
                }
                sequence_status.append(error_info)

        return success_response(
            f"Retrieved status for {len(sequences)} sequences. {out_of_sync_count} sequences are out of sync.",
            {
                "sequences": sequence_status,
                "total_sequences": len(sequences),
                "out_of_sync_count": out_of_sync_count,
                "summary": {
                    "operation": "sequence_status_check",
                    "timestamp": str(db.execute(text("SELECT NOW()")).scalar()),
                    "needs_reset": out_of_sync_count > 0
                }
            }
        )

    except Exception as e:
        logger.error(f"Error getting sequence status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "message": f"Failed to get sequence status: {str(e)}",
                "status_code": 500
            }
        )
