from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from uuid import UUID

# Schema for creating a CoMember
class CoMemberCreate(BaseModel):
    password: str = Field(..., description="Password for the member")
    customerIoId: Optional[str] = Field(None)
    openWaterId: Optional[int] = Field(None)
    firstName: Optional[str] = Field(None)
    lastName: Optional[str] = Field(None)
    loginEmail: EmailStr = Field(..., description="Login email of the member")
    loginEmailVerified: bool = Field(True)
    identityType: Optional[str] = Field(None)
    personalBusinessEmail: Optional[EmailStr] = Field(None)
    phone: Optional[str] = Field(None)
    professionalTitle: Optional[str] = Field(None)
    membershipTier: str = Field("lite", description="Membership tier of the member")
    communityStatus: str = Field("unverified", description="Status of the member in the community")
    hasSeenFirstLoginMessage: bool = Field(False, description="Whether the member has seen the first login message")

    class Config:
        from_attributes = True

# Schema for updating a CoMember (password is optional)
class CoMemberUpdate(BaseModel):
    password: Optional[str] = Field(None, description="Password for the member (optional for updates)")
    customerIoId: Optional[str] = Field(None)
    openWaterId: Optional[int] = Field(None)
    firstName: Optional[str] = Field(None)
    lastName: Optional[str] = Field(None)
    loginEmail: Optional[EmailStr] = Field(None, description="Login email of the member")
    loginEmailVerified: Optional[bool] = Field(None)
    identityType: Optional[str] = Field(None)
    personalBusinessEmail: Optional[EmailStr] = Field(None)
    phone: Optional[str] = Field(None)
    professionalTitle: Optional[str] = Field(None)
    membershipTier: Optional[str] = Field(None, description="Membership tier of the member")
    communityStatus: Optional[str] = Field(None, description="Status of the member in the community")
    hasSeenFirstLoginMessage: Optional[bool] = Field(None, description="Whether the member has seen the first login message")

    class Config:
        from_attributes = True

# Schema for fetching a CoMember
class CoMemberResponse(BaseModel):
    uuid: UUID = Field(None, description="UUID of the member")
    customerIoId: Optional[str] = Field(None, description="Customer.io identifier")
    openWaterId: Optional[int] = Field(None, description="OpenWater identifier")
    firstName: Optional[str] = Field(None, description="First name of the member")
    lastName: Optional[str] = Field(None, description="Last name of the member")
    loginEmail: Optional[EmailStr] = Field(None, description="Login email of the member")
    loginEmailVerified: bool = Field(False, description="Whether the login email is verified")
    identityType: Optional[str] = Field(None, description="Type of identity")
    personalBusinessEmail: Optional[EmailStr] = Field(None, description="Personal or business email")
    phone: Optional[str] = Field(None, description="Phone number of the member")
    professionalTitle: Optional[str] = Field(None, description="Professional title of the member")
    membershipTier: str = Field(..., description="Membership tier of the member")
    communityStatus: str = Field("unverified", description="Status of the member in the community")
    hasSeenFirstLoginMessage: bool = Field(False, description="Whether the member has seen the first login message")

    class Config:
        from_attributes = True

class LoginRequest(BaseModel):
    loginEmail: str = Field("<EMAIL>", description="Login email of the member")
    password: str = Field("Admin@123", description="Password for the member")
    
class Provider(BaseModel):
    provider: str