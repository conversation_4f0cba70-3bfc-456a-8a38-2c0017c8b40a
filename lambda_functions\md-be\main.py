from fastapi import Depends, FastAPI
from sqlalchemy.orm import Session
from controller.member_controller import Member<PERSON><PERSON>roller
from routes import member_authentication_router, member_router, health_router
from db.db import Base, get_db, get_engine
from fastapi.middleware.cors import CORSMiddleware
from models import rbac
from models import admin
from models import member
from models import organization
from models import feature
from models.rbac import Role, Module, RoleModulePermission
from models.admin import AdminModel
from models.member import CoMember, CoAuth0User
from models.organization import CoOrganization, CoOrganizationVerifiedData, CoRelationsMembersOrganizations, CoMembersAwards
from models.feature import CoFeatureFlag, CoMemberBookmark
from routes import admins_router, rbac_router, organization_router, feature_router, award_router
from slowapi import Limiter
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from fastapi.responses import JSONResponse
from slowapi.middleware import SlowAPIMiddleware
from mangum import Mangum
from services import rbac_api
import uvicorn
import logging

# Import all models to ensure they are registered with SQLAlchemy
from models import *

logger = logging.getLogger(__name__)


app = FastAPI()
memberController = MemberController()

limiter = Limiter(key_func=get_remote_address, default_limits=["40/minute"])
app.state.limiter = limiter

@app.exception_handler(RateLimitExceeded)
def rate_limit_handler(request, exc):
    return JSONResponse(
        status_code=429,
        content={
            "detail": {
                "message": "Rate limit exceeded",
                "status_code": 429
            }
        },
    )

# Enable CORS for all origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(SlowAPIMiddleware)

# Create all tables after importing all models
# Note: Tables will be created on first database access due to lazy initialization

@app.get("/", tags=["root"])
async def root():
    return {"message": "Welcome to the US Chamber of Commerce API"}

@app.get("/get-roles", tags=["Check api health (fetching Roles)"])
async def get_roles(db: Session = Depends(get_db)):
    roles = rbac_api.get_all_roles(db, perPage=1, pageSize=10)
    return roles

@app.get("/test-db-connection", tags=["Database Connection Test"])
async def test_db_connection():
    """Test database connection and log credentials from Secrets Manager"""
    from db.connection_manager import initialize_connection_manager
    from db.secrets_manager_auth import initialize_secrets_auth
    import traceback
    
    try:
        # Step 1: Test Secrets Manager access first
        logger.info("🔍 Testing Secrets Manager access...")
        secrets_auth = initialize_secrets_auth("stg_member_db_rw_user", "us-east-1")
        secret_test = secrets_auth.test_secret_access()
        
        if secret_test["status"] == "error":
            return {
                "status": "failed",
                "step": "secrets_manager_access",
                "message": "Failed to access AWS Secrets Manager",
                "error_details": secret_test,
                "possible_causes": [
                    "Lambda execution role doesn't have Secrets Manager permissions",
                    "Secret 'stg_member_db_rw_user' doesn't exist",
                    "Secret is in different region than 'us-east-1'",
                    "AWS credentials not properly configured"
                ]
            }
        
        # Step 2: Initialize connection manager
        logger.info("🔗 Initializing database connection manager...")
        connection_manager = initialize_connection_manager(
            secret_name="stg_member_db_rw_user",
            region="us-east-1"
        )
        
        # Step 3: Test the connection
        logger.info("🧪 Testing database connection...")
        test_result = connection_manager.test_connection()
        
        if test_result["status"] == "success":
            return {
                "status": "success",
                "message": "Database connection test completed successfully",
                "test_result": test_result,
                "connection_info": connection_manager.get_connection_info(),
                "logs": connection_manager.get_logs(),
                "summary": {
                    "secrets_manager": "✅ Accessible",
                    "credentials": "✅ Retrieved",
                    "database_connection": "✅ Successful"
                }
            }
        else:
            return {
                "status": "failed",
                "step": "database_connection",
                "message": "Database connection failed",
                "error_details": test_result,
                "connection_info": connection_manager.get_connection_info(),
                "logs": connection_manager.get_logs(),
                "possible_causes": [
                    "Database credentials are incorrect",
                    "Database server is not accessible",
                    "Network/VPC configuration issues",
                    "Database server is down",
                    "SSL certificate issues"
                ]
            }
            
    except Exception as e:
        error_traceback = traceback.format_exc()
        logger.error(f"❌ Unexpected error in database connection test: {str(e)}")
        logger.error(f"Traceback: {error_traceback}")
        
        return {
            "status": "failed",
            "step": "unexpected_error",
            "message": "Unexpected error occurred during database connection test",
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": error_traceback,
            "possible_causes": [
                "Missing dependencies",
                "Configuration errors",
                "AWS SDK issues",
                "Network connectivity problems"
            ]
        }

@app.get("/test-secrets-access", tags=["Secrets Manager Test"])
async def test_secrets_access():
    """Test AWS Secrets Manager access without database connection"""
    from db.secrets_manager_auth import initialize_secrets_auth
    import traceback
    
    try:
        logger.info("🔍 Testing Secrets Manager access only...")
        secrets_auth = initialize_secrets_auth("stg_member_db_rw_user", "us-east-1")
        
        # Test secret access
        secret_test = secrets_auth.test_secret_access()
        
        if secret_test["status"] == "success":
            return {
                "status": "success",
                "message": "Secrets Manager access successful",
                "secret_info": secret_test,
                "logs": secret_test.get("logs", {}),
                "summary": {
                    "secrets_manager": "✅ Accessible",
                    "secret_exists": "✅ Found",
                    "credentials_available": "✅ Retrieved"
                }
            }
        else:
            return {
                "status": "failed",
                "message": "Secrets Manager access failed",
                "error_details": secret_test,
                "logs": secret_test.get("logs", {}),
                "troubleshooting_steps": [
                    "Check Lambda execution role has 'secretsmanager:GetSecretValue' permission",
                    "Verify secret 'stg_member_db_rw_user' exists in us-east-1 region",
                    "Ensure Lambda is in the correct VPC if required",
                    "Check CloudWatch logs for detailed error messages"
                ]
            }
            
    except Exception as e:
        error_traceback = traceback.format_exc()
        logger.error(f"❌ Unexpected error in Secrets Manager test: {str(e)}")
        logger.error(f"Traceback: {error_traceback}")
        
        return {
            "status": "failed",
            "message": "Unexpected error occurred during Secrets Manager test",
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": error_traceback
        }

app.include_router(health_router.router, prefix="", tags=["Health Checks"])
app.include_router(member_authentication_router.router, prefix="/api/members", tags=["Member Authentication"])
app.include_router(member_router.router, prefix="/api/members", tags=["Members"])
app.include_router(admins_router.router, prefix="/api/admin", tags=["Admin"])
app.include_router(rbac_router.router, prefix="/api/rbac", tags=["RBAC"])
app.include_router(organization_router.router, prefix="/api/organizations", tags=["Organizations"])

# to allow AWS Lambda to run the app
def handler(event, context):
    mangum_handler = Mangum(app)
    return mangum_handler(event, context)

if __name__ == "__main__":
    uvicorn.run(
        "main:app"
        # Enable auto-reload on code changes
    )
